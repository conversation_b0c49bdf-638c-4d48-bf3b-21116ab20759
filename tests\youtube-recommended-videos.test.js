// Mock electron modules before importing main
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn().mockReturnValue('/mock/user/data'),
    getVersion: jest.fn().mockReturnValue('1.0.0')
  },
  BrowserWindow: jest.fn(),
  BrowserView: jest.fn(),
  Tray: jest.fn(),
  Menu: jest.fn(),
  ipcMain: { on: jest.fn() },
  screen: { getPrimaryDisplay: jest.fn() },
  shell: { openExternal: jest.fn() }
}));

// Mock electron-store
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }));
});

// Mock auto-launch
jest.mock('auto-launch', () => {
  return jest.fn().mockImplementation(() => ({
    isEnabled: jest.fn().mockResolvedValue(false),
    enable: jest.fn().mockResolvedValue(),
    disable: jest.fn().mockResolvedValue()
  }));
});

// Mock fs module
jest.mock('fs');

const YSViewerApp = require('../src/main');

describe('YouTube Recommended Video Detection', () => {
  let app;
  let mockYouTubeView;
  let mockWebContents;

  beforeEach(() => {
    // Mock YouTube view and web contents
    mockWebContents = {
      executeJavaScript: jest.fn(),
      isDestroyed: jest.fn().mockReturnValue(false),
      loadURL: jest.fn()
    };

    mockYouTubeView = {
      webContents: mockWebContents
    };

    // Create app instance with mocked views
    app = new YSViewerApp();
    app.youtubeView = mockYouTubeView;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('extractVideoId', () => {
    test('should extract video ID from youtube.com/watch URL', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should extract video ID from youtu.be URL', () => {
      const url = 'https://youtu.be/dQw4w9WgXcQ';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should handle URLs with additional parameters', () => {
      const url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy6nuLMt9xUCce0-WvQvckS&index=1';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBe('dQw4w9WgXcQ');
    });

    test('should return null for invalid URLs', () => {
      const url = 'https://example.com/invalid';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBeNull();
    });

    test('should handle malformed URLs gracefully', () => {
      const url = 'not-a-url';
      const videoId = app.extractVideoId(url);
      expect(videoId).toBeNull();
    });
  });

  describe('tryClickRecommendedVideo', () => {
    test('should find and click matching recommended video', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock successful JavaScript execution
      mockWebContents.executeJavaScript.mockResolvedValue(true);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(true);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining("const targetVideoId = 'dQw4w9WgXcQ'")
      );
    });

    test('should return false when no matching video found', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=notfound123';
      
      // Mock JavaScript execution returning false (no match found)
      mockWebContents.executeJavaScript.mockResolvedValue(false);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).toHaveBeenCalled();
    });

    test('should handle destroyed web contents gracefully', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock destroyed web contents
      mockWebContents.isDestroyed.mockReturnValue(true);

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });

    test('should handle JavaScript execution errors', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
      
      // Mock JavaScript execution error
      mockWebContents.executeJavaScript.mockRejectedValue(new Error('JavaScript error'));

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
    });

    test('should handle invalid target URLs', async () => {
      const targetUrl = 'invalid-url';

      const result = await app.tryClickRecommendedVideo(targetUrl);

      expect(result).toBe(false);
      expect(mockWebContents.executeJavaScript).not.toHaveBeenCalled();
    });
  });

  describe('JavaScript execution for recommended videos', () => {
    test('should generate correct JavaScript for video detection', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=testVideoId';
      
      mockWebContents.executeJavaScript.mockResolvedValue(true);

      await app.tryClickRecommendedVideo(targetUrl);

      const jsCode = mockWebContents.executeJavaScript.mock.calls[0][0];
      
      // Verify the JavaScript contains the correct video ID
      expect(jsCode).toContain("const targetVideoId = 'testVideoId'");
      
      // Verify it looks for video links
      expect(jsCode).toContain('document.querySelectorAll(\'a[href*="/watch?v="], a[href*="youtu.be/"]\'');
      
      // Verify it handles tracking parameters
      expect(jsCode).toContain('trackingParams');
      expect(jsCode).toContain('list');
      expect(jsCode).toContain('index');
      expect(jsCode).toContain('pp');
      expect(jsCode).toContain('si');
      expect(jsCode).toContain('feature');
      expect(jsCode).toContain('app');
      expect(jsCode).toContain('itct');
      
      // Verify it clicks the link
      expect(jsCode).toContain('link.click()');
    });
  });

  describe('Mock Data Collection', () => {
    test('should collect execution data for recommended video detection', async () => {
      const targetUrl = 'https://www.youtube.com/watch?v=mockVideo123';
      
      // Mock various scenarios
      const scenarios = [
        { found: true, clicked: true },
        { found: true, clicked: false },
        { found: false, clicked: false }
      ];

      const executionData = [];

      for (const scenario of scenarios) {
        const startTime = Date.now();
        mockWebContents.executeJavaScript.mockResolvedValue(scenario.clicked);
        
        const result = await app.tryClickRecommendedVideo(targetUrl);
        const endTime = Date.now();
        
        executionData.push({
          scenario: scenario,
          result: result,
          executionTime: endTime - startTime,
          success: result === scenario.clicked
        });
      }

      // Verify we collected data for all scenarios
      expect(executionData).toHaveLength(3);
      
      // Calculate success rate
      const successfulExecutions = executionData.filter(data => data.success).length;
      const successRate = successfulExecutions / executionData.length;
      
      expect(successRate).toBeGreaterThanOrEqual(0);
      expect(successRate).toBeLessThanOrEqual(1);
      
      console.log('Recommended video detection execution data:', {
        totalExecutions: executionData.length,
        successfulExecutions: successfulExecutions,
        successRate: successRate,
        averageExecutionTime: executionData.reduce((sum, data) => sum + data.executionTime, 0) / executionData.length,
        scenarios: executionData.map(data => ({
          scenario: data.scenario,
          result: data.result,
          executionTime: data.executionTime
        }))
      });
    });
  });
});
